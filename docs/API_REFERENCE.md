# Image GPU API Reference

This document provides a comprehensive reference for the Image GPU library API.

## Core Types

### Image

The main image data structure supporting GPU acceleration.

```mojo
struct Image:
    var width: Int
    var height: Int
    var channels: Int
    var format: ImageFormat
    var data_type: DataType
    var color_space: ColorSpace
    var device_type: DeviceType
```

#### Constructors

```mojo
fn __init__(inout self, width: Int, height: Int, format: ImageFormat = ImageFormat.RGB, 
            data_type: DataType = DataType.UINT8, color_space: ColorSpace = ColorSpace.SRGB,
            device_type: DeviceType = DeviceType.CPU)
```

#### Methods

- `size_bytes() -> Int` - Returns total size of image data in bytes
- `get_pixel_uint8(x: Int, y: Int) -> Pixel` - Get pixel value at coordinates
- `set_pixel_uint8(x: Int, y: Int, pixel: Pixel)` - Set pixel value at coordinates
- `is_valid_coords(x: Int, y: Int) -> Bool` - Check if coordinates are valid
- `fill(pixel: Pixel)` - Fill entire image with a pixel value
- `clone() -> Image` - Create a deep copy of the image

### Pixel

Represents a single pixel with RGBA channels.

```mojo
struct Pixel:
    var r: Float32
    var g: Float32
    var b: Float32
    var a: Float32
```

#### Constructors

```mojo
fn __init__(inout self, r: Float32 = 0.0, g: Float32 = 0.0, b: Float32 = 0.0, a: Float32 = 1.0)
fn __init__(inout self, gray: Float32)  # Grayscale constructor
```

#### Methods

- `luminance() -> Float32` - Calculate luminance using standard weights
- `to_grayscale() -> Pixel` - Convert to grayscale pixel
- `clamp()` - Clamp values to [0.0, 1.0] range

## Enumerations

### ImageFormat

```mojo
struct ImageFormat:
    alias UNKNOWN = ImageFormat(0)
    alias RGB = ImageFormat(1)
    alias RGBA = ImageFormat(2)
    alias GRAYSCALE = ImageFormat(3)
    alias BGR = ImageFormat(4)
    alias BGRA = ImageFormat(5)
```

### DataType

```mojo
struct DataType:
    alias UINT8 = DataType(0)
    alias UINT16 = DataType(1)
    alias FLOAT32 = DataType(2)
    alias FLOAT64 = DataType(3)
```

### DeviceType

```mojo
struct DeviceType:
    alias CPU = DeviceType(0)
    alias CUDA = DeviceType(1)
    alias OPENCL = DeviceType(2)
    alias METAL = DeviceType(3)
```

## I/O Functions

### Loading Images

```mojo
fn load_image(file_path: String, device_type: DeviceType = DeviceType.CPU) raises -> Image
```

Load an image from a file. Supports PNG, JPEG, BMP formats.

```mojo
fn create_test_image(width: Int, height: Int, format: ImageFormat, device_type: DeviceType) -> Image
```

Create a test image with gradient pattern.

```mojo
fn create_solid_color_image(width: Int, height: Int, r: Float32, g: Float32, b: Float32, 
                           a: Float32 = 1.0, format: ImageFormat = ImageFormat.RGB, 
                           device_type: DeviceType = DeviceType.CPU) -> Image
```

Create an image filled with solid color.

### Saving Images

```mojo
fn save_image(image: Image, file_path: String) raises
```

Save an image to a file. Format determined by file extension.

## Image Operations

### Geometric Operations

```mojo
fn resize_image(image: Image, new_width: Int, new_height: Int, 
               interpolation: String = "bilinear") -> Image
```

Resize image using specified interpolation method ("nearest", "bilinear", "bicubic").

```mojo
fn crop_image(image: Image, x: Int, y: Int, width: Int, height: Int) -> Image
```

Crop rectangular region from image.

```mojo
fn rotate_image(image: Image, angle_degrees: Float32) -> Image
```

Rotate image by specified angle in degrees.

```mojo
fn flip_horizontal(image: Image) -> Image
fn flip_vertical(image: Image) -> Image
```

Flip image horizontally or vertically.

### Color Operations

```mojo
fn convert_to_grayscale(image: Image) -> Image
```

Convert image to grayscale.

```mojo
fn adjust_brightness(image: Image, factor: Float32) -> Image
```

Adjust image brightness (1.0 = no change, >1.0 = brighter, <1.0 = darker).

```mojo
fn adjust_contrast(image: Image, factor: Float32) -> Image
```

Adjust image contrast (1.0 = no change, >1.0 = more contrast, <1.0 = less contrast).

## Filters

### Blur Filters

```mojo
fn gaussian_blur(image: Image, sigma: Float32, kernel_size: Int = 0) -> Image
```

Apply Gaussian blur with specified standard deviation.

```mojo
fn box_blur(image: Image, radius: Int) -> Image
```

Apply box blur with specified radius.

```mojo
fn motion_blur(image: Image, length: Int, angle: Float32) -> Image
```

Apply motion blur with specified length and angle.

### Edge Detection

```mojo
fn edge_detect(image: Image, method: String = "sobel", threshold: Float32 = 0.1) -> Image
```

Detect edges using specified method ("sobel", "laplacian", "canny").

```mojo
fn sobel_filter(image: Image, threshold: Float32 = 0.1) -> Image
```

Apply Sobel edge detection filter.

```mojo
fn laplacian_filter(image: Image, threshold: Float32 = 0.1) -> Image
```

Apply Laplacian edge detection filter.

## GPU Operations

### Device Management

```mojo
struct DeviceManager:
    fn __init__(inout self)
    fn get_device_count() -> Int
    fn get_device(device_id: Int) -> GPUDevice
    fn get_current_device() -> GPUDevice
    fn set_current_device(device_id: Int)
    fn get_best_device(prefer_type: DeviceType = DeviceType.CUDA) -> GPUDevice
```

### GPU Memory

```mojo
struct GPUBuffer:
    fn __init__(inout self, size_bytes: Int, device_type: DeviceType = DeviceType.CUDA)
    fn copy_to_device()
    fn copy_to_host()
    fn get_device_ptr() -> UnsafePointer[UInt8]
    fn get_host_ptr() -> UnsafePointer[UInt8]
    fn synchronize()
```

### Custom Kernels

```mojo
fn gpu_convolution(image: Image, kernel: UnsafePointer[Float32], kernel_size: Int) -> Image
```

Apply custom convolution kernel using GPU acceleration.

```mojo
fn create_gaussian_kernel(size: Int, sigma: Float32) -> UnsafePointer[Float32]
```

Create Gaussian convolution kernel.

## Usage Examples

### Basic Image Processing

```mojo
from image_gpu import Image, load_image, save_image
from image_gpu.filters import gaussian_blur, edge_detect

# Load image
var img = load_image("input.jpg")

# Apply filters
var blurred = gaussian_blur(img, sigma=2.0)
var edges = edge_detect(blurred, method="sobel")

# Save result
save_image(edges, "output.png")
```

### GPU-Accelerated Processing

```mojo
from image_gpu.gpu.device import DeviceManager
from image_gpu.core.types import DeviceType

# Initialize GPU
var device_manager = DeviceManager()
var gpu_device = device_manager.get_best_device(DeviceType.CUDA)

# Create image on GPU
var gpu_image = create_test_image(1024, 1024, ImageFormat.RGB, DeviceType.CUDA)

# Process on GPU
var result = gaussian_blur(gpu_image, sigma=3.0)
```

### Custom Kernel Processing

```mojo
from image_gpu.gpu.kernels import gpu_convolution

# Create custom sharpen kernel
var kernel = UnsafePointer[Float32].alloc(9)
kernel[0] =  0.0; kernel[1] = -1.0; kernel[2] =  0.0
kernel[3] = -1.0; kernel[4] =  5.0; kernel[5] = -1.0
kernel[6] =  0.0; kernel[7] = -1.0; kernel[8] =  0.0

# Apply custom kernel
var sharpened = gpu_convolution(image, kernel, 3)

# Cleanup
kernel.free()
```

## Error Handling

Most functions that can fail use Mojo's `raises` mechanism:

```mojo
try:
    var img = load_image("nonexistent.jpg")
except:
    print("Failed to load image")
```

## Performance Considerations

1. **GPU Memory**: Images are automatically transferred between CPU and GPU memory as needed
2. **Batch Processing**: Process multiple images together for better GPU utilization
3. **Memory Management**: Images automatically clean up their memory when destroyed
4. **Device Selection**: Use `DeviceManager` to select the best available GPU device

## Thread Safety

The library is designed to be thread-safe for read operations. Write operations should be synchronized by the user when accessing the same image from multiple threads.
