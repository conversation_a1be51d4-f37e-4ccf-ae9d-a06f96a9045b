"""
Basic Image Filtering Example

This example demonstrates basic image filtering operations using the image-gpu library.
It shows how to load an image, apply various filters, and save the results.
"""

from image_gpu import Image, load_image, save_image
from image_gpu.filters import gaussian_blur, edge_detect
from image_gpu.core.operations import resize_image, convert_to_grayscale, adjust_brightness
from image_gpu.io.loader import create_solid_color_image, create_test_image

fn main():
    """Main function demonstrating basic filtering operations."""
    print("Image GPU - Basic Filtering Example")
    print("===================================")
    
    # Create a test image since we don't have actual image loading yet
    print("Creating test image...")
    var original_image = create_test_image(512, 512, ImageFormat.RGB, DeviceType.CPU)
    print("Test image created: " + str(original_image.width) + "x" + str(original_image.height))
    
    # Apply Gaussian blur
    print("\nApplying Gaussian blur...")
    let blurred = gaussian_blur(original_image, sigma=2.0)
    print("Gaussian blur applied with sigma=2.0")
    
    # Apply edge detection
    print("\nApplying edge detection...")
    let edges = edge_detect(blurred, method="sobel", threshold=0.1)
    print("Edge detection applied using Sobel method")
    
    # Convert to grayscale
    print("\nConverting to grayscale...")
    let grayscale = convert_to_grayscale(original_image)
    print("Image converted to grayscale")
    
    # Resize image
    print("\nResizing image...")
    let resized = resize_image(original_image, 256, 256, "bilinear")
    print("Image resized to 256x256 using bilinear interpolation")
    
    # Adjust brightness
    print("\nAdjusting brightness...")
    let brightened = adjust_brightness(original_image, 1.5)
    print("Brightness increased by 50%")
    
    # Create a solid color image
    print("\nCreating solid color image...")
    let red_image = create_solid_color_image(200, 200, 1.0, 0.0, 0.0, 1.0)
    print("Red solid color image created: 200x200")
    
    # Apply multiple filters in sequence
    print("\nApplying multiple filters in sequence...")
    var processed = original_image
    processed = gaussian_blur(processed, sigma=1.0)
    processed = adjust_brightness(processed, 1.2)
    processed = resize_image(processed, 400, 400, "bilinear")
    print("Applied blur -> brightness -> resize sequence")
    
    # Demonstrate different blur types
    print("\nDemonstrating different blur types...")
    let box_blurred = box_blur(original_image, radius=3)
    let motion_blurred = motion_blur(original_image, length=15, angle=45.0)
    print("Applied box blur (radius=3) and motion blur (length=15, angle=45°)")
    
    # Demonstrate different edge detection methods
    print("\nDemonstrating different edge detection methods...")
    let sobel_edges = edge_detect(original_image, method="sobel")
    let laplacian_edges = edge_detect(original_image, method="laplacian")
    print("Applied Sobel and Laplacian edge detection")
    
    # Performance comparison
    print("\nPerformance comparison...")
    let start_time = time.now()
    
    # CPU processing
    var cpu_image = original_image
    cpu_image.device_type = DeviceType.CPU
    let cpu_result = gaussian_blur(cpu_image, sigma=2.0)
    let cpu_time = time.now() - start_time
    
    # GPU processing (simulated)
    let gpu_start = time.now()
    var gpu_image = original_image
    gpu_image.device_type = DeviceType.CUDA
    let gpu_result = gaussian_blur(gpu_image, sigma=2.0)
    let gpu_time = time.now() - gpu_start
    
    print("CPU processing time: " + str(cpu_time) + "ms")
    print("GPU processing time: " + str(gpu_time) + "ms")
    print("Speedup: " + str(cpu_time / gpu_time) + "x")
    
    print("\nBasic filtering example completed successfully!")
    print("In a real implementation, processed images would be saved to disk.")

# Import required modules
from image_gpu.core.types import ImageFormat, DeviceType
from image_gpu.filters.blur import box_blur, motion_blur
import time

# Run the example
if __name__ == "__main__":
    main()
