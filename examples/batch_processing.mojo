"""
Batch Image Processing Example

This example demonstrates how to process multiple images efficiently
using the image-gpu library with GPU acceleration.
"""

from image_gpu import Image
from image_gpu.filters import gaussian_blur, edge_detect
from image_gpu.core.operations import resize_image, adjust_brightness, adjust_contrast
from image_gpu.io.loader import create_test_image, create_solid_color_image
from image_gpu.gpu.device import <PERSON>ceManager
from image_gpu.core.types import ImageFormat, DeviceType

struct BatchProcessor:
    """Batch processor for handling multiple images efficiently."""
    var device_manager: DeviceManager
    var current_device: GPUDevice
    var processed_count: Int
    
    fn __init__(inout self):
        """Initialize the batch processor."""
        self.device_manager = DeviceManager()
        self.current_device = self.device_manager.get_best_device()
        self.processed_count = 0
        print("Batch processor initialized with device: " + self.current_device.name)
    
    fn process_image_set(inout self, images: List[Image], operations: List[String]) -> List[Image]:
        """
        Process a set of images with the specified operations.
        
        Args:
            images: List of input images
            operations: List of operation names to apply
        
        Returns:
            List of processed images
        """
        var results = List[Image]()
        
        print("Processing " + str(len(images)) + " images with " + str(len(operations)) + " operations...")
        
        for i in range(len(images)):
            var current_image = images[i]
            
            # Apply each operation in sequence
            for j in range(len(operations)):
                let operation = operations[j]
                current_image = self._apply_operation(current_image, operation)
            
            results.append(current_image)
            self.processed_count += 1
            
            if (i + 1) % 10 == 0:
                print("Processed " + str(i + 1) + "/" + str(len(images)) + " images")
        
        return results
    
    fn _apply_operation(self, image: Image, operation: String) -> Image:
        """Apply a single operation to an image."""
        if operation == "blur":
            return gaussian_blur(image, sigma=1.5)
        elif operation == "edge":
            return edge_detect(image, method="sobel")
        elif operation == "resize":
            return resize_image(image, image.width // 2, image.height // 2)
        elif operation == "brighten":
            return adjust_brightness(image, 1.3)
        elif operation == "contrast":
            return adjust_contrast(image, 1.2)
        else:
            return image  # No operation
    
    fn get_stats(self) -> (Int, String):
        """Get processing statistics."""
        return (self.processed_count, self.current_device.name)

fn create_test_image_set(count: Int, width: Int, height: Int) -> List[Image]:
    """Create a set of test images for batch processing."""
    var images = List[Image]()
    
    print("Creating " + str(count) + " test images...")
    
    for i in range(count):
        # Create different types of test images
        if i % 4 == 0:
            # Gradient test image
            let img = create_test_image(width, height, ImageFormat.RGB, DeviceType.CPU)
            images.append(img)
        elif i % 4 == 1:
            # Red solid color
            let img = create_solid_color_image(width, height, 1.0, 0.0, 0.0)
            images.append(img)
        elif i % 4 == 2:
            # Green solid color
            let img = create_solid_color_image(width, height, 0.0, 1.0, 0.0)
            images.append(img)
        else:
            # Blue solid color
            let img = create_solid_color_image(width, height, 0.0, 0.0, 1.0)
            images.append(img)
    
    return images

fn benchmark_batch_processing():
    """Benchmark batch processing performance."""
    print("\nBatch Processing Benchmark")
    print("==========================")
    
    # Create test images
    let image_count = 50
    let test_images = create_test_image_set(image_count, 256, 256)
    
    # Define processing pipelines
    var simple_pipeline = List[String]()
    simple_pipeline.append("blur")
    simple_pipeline.append("brighten")
    
    var complex_pipeline = List[String]()
    complex_pipeline.append("blur")
    complex_pipeline.append("edge")
    complex_pipeline.append("resize")
    complex_pipeline.append("contrast")
    
    # Initialize batch processor
    var processor = BatchProcessor()
    
    # Benchmark simple pipeline
    print("\nTesting simple pipeline (blur + brighten)...")
    let simple_start = time.now()
    let simple_results = processor.process_image_set(test_images, simple_pipeline)
    let simple_time = time.now() - simple_start
    
    print("Simple pipeline completed in " + str(simple_time) + "ms")
    print("Average time per image: " + str(simple_time / Float64(image_count)) + "ms")
    
    # Benchmark complex pipeline
    print("\nTesting complex pipeline (blur + edge + resize + contrast)...")
    let complex_start = time.now()
    let complex_results = processor.process_image_set(test_images, complex_pipeline)
    let complex_time = time.now() - complex_start
    
    print("Complex pipeline completed in " + str(complex_time) + "ms")
    print("Average time per image: " + str(complex_time / Float64(image_count)) + "ms")
    
    # Get final statistics
    let (total_processed, device_name) = processor.get_stats()
    print("\nFinal Statistics:")
    print("Total images processed: " + str(total_processed))
    print("Processing device: " + device_name)

fn demonstrate_parallel_processing():
    """Demonstrate parallel processing capabilities."""
    print("\nParallel Processing Demonstration")
    print("=================================")
    
    # Create larger test set
    let large_image_set = create_test_image_set(100, 512, 512)
    
    # Simulate parallel processing by dividing work
    let batch_size = 25
    let num_batches = len(large_image_set) // batch_size
    
    var operations = List[String]()
    operations.append("blur")
    operations.append("edge")
    
    print("Processing " + str(len(large_image_set)) + " images in " + str(num_batches) + " batches...")
    
    let parallel_start = time.now()
    
    for batch_idx in range(num_batches):
        let start_idx = batch_idx * batch_size
        let end_idx = min(start_idx + batch_size, len(large_image_set))
        
        # Create batch
        var batch_images = List[Image]()
        for i in range(start_idx, end_idx):
            batch_images.append(large_image_set[i])
        
        # Process batch
        var batch_processor = BatchProcessor()
        let batch_results = batch_processor.process_image_set(batch_images, operations)
        
        print("Completed batch " + str(batch_idx + 1) + "/" + str(num_batches))
    
    let parallel_time = time.now() - parallel_start
    print("Parallel processing completed in " + str(parallel_time) + "ms")
    print("Throughput: " + str(Float64(len(large_image_set)) / parallel_time * 1000.0) + " images/second")

fn main():
    """Main function demonstrating batch processing capabilities."""
    print("Image GPU - Batch Processing Example")
    print("====================================")
    
    # Run batch processing benchmark
    benchmark_batch_processing()
    
    # Demonstrate parallel processing
    demonstrate_parallel_processing()
    
    # Memory usage demonstration
    print("\nMemory Usage Demonstration")
    print("==========================")
    
    # Process images of different sizes
    let sizes = [(128, 128), (256, 256), (512, 512), (1024, 1024)]
    
    for size_idx in range(len(sizes)):
        let (width, height) = sizes[size_idx]
        let test_image = create_test_image(width, height, ImageFormat.RGB, DeviceType.CPU)
        
        let memory_usage = test_image.size_bytes()
        print("Image " + str(width) + "x" + str(height) + " uses " + str(memory_usage) + " bytes")
        
        # Apply processing
        let processed = gaussian_blur(test_image, sigma=2.0)
        print("Processing completed for " + str(width) + "x" + str(height) + " image")
    
    print("\nBatch processing example completed successfully!")

# Import required modules
from image_gpu.gpu.device import GPUDevice
import time

# Run the example
if __name__ == "__main__":
    main()
