"""
Custom GPU Kernels Example

This example demonstrates how to create and use custom GPU kernels
for specialized image processing operations.
"""

from image_gpu import Image
from image_gpu.gpu.kernels import Convolution<PERSON>ernel, gpu_convolution
from image_gpu.gpu.device import Device<PERSON>anager, GPUDevice
from image_gpu.gpu.memory import GP<PERSON><PERSON>uffer
from image_gpu.io.loader import create_test_image
from image_gpu.core.types import ImageFormat, DeviceType

fn create_sharpen_kernel() -> UnsafePointer[Float32]:
    """Create a sharpening kernel."""
    let kernel = UnsafePointer[Float32].alloc(9)
    kernel[0] =  0.0; kernel[1] = -1.0; kernel[2] =  0.0
    kernel[3] = -1.0; kernel[4] =  5.0; kernel[5] = -1.0
    kernel[6] =  0.0; kernel[7] = -1.0; kernel[8] =  0.0
    return kernel

fn create_emboss_kernel() -> UnsafePointer[Float32]:
    """Create an emboss effect kernel."""
    let kernel = UnsafePointer[Float32].alloc(9)
    kernel[0] = -2.0; kernel[1] = -1.0; kernel[2] =  0.0
    kernel[3] = -1.0; kernel[4] =  1.0; kernel[5] =  1.0
    kernel[6] =  0.0; kernel[7] =  1.0; kernel[8] =  2.0
    return kernel

fn create_outline_kernel() -> UnsafePointer[Float32]:
    """Create an outline detection kernel."""
    let kernel = UnsafePointer[Float32].alloc(9)
    kernel[0] = -1.0; kernel[1] = -1.0; kernel[2] = -1.0
    kernel[3] = -1.0; kernel[4] =  8.0; kernel[5] = -1.0
    kernel[6] = -1.0; kernel[7] = -1.0; kernel[8] = -1.0
    return kernel

fn create_ridge_detection_kernel() -> UnsafePointer[Float32]:
    """Create a ridge detection kernel."""
    let kernel = UnsafePointer[Float32].alloc(25)  # 5x5 kernel
    
    # Ridge detection kernel (5x5)
    let values = [
        -1.0, -1.0, -1.0, -1.0, -1.0,
        -1.0, -1.0, -1.0, -1.0, -1.0,
        -1.0, -1.0, 24.0, -1.0, -1.0,
        -1.0, -1.0, -1.0, -1.0, -1.0,
        -1.0, -1.0, -1.0, -1.0, -1.0
    ]
    
    for i in range(25):
        kernel[i] = values[i]
    
    return kernel

fn create_custom_blur_kernel(size: Int, strength: Float32) -> UnsafePointer[Float32]:
    """Create a custom blur kernel with variable strength."""
    let kernel = UnsafePointer[Float32].alloc(size * size)
    let center = size // 2
    var total: Float32 = 0.0
    
    # Create distance-based blur
    for y in range(size):
        for x in range(size):
            let dx = Float32(x - center)
            let dy = Float32(y - center)
            let distance = sqrt(dx * dx + dy * dy)
            let value = exp(-distance * strength)
            
            kernel[y * size + x] = value
            total += value
    
    # Normalize kernel
    for i in range(size * size):
        kernel[i] /= total
    
    return kernel

struct CustomKernelProcessor:
    """Processor for custom kernel operations."""
    var device: GPUDevice
    var device_manager: DeviceManager
    
    fn __init__(inout self):
        """Initialize the custom kernel processor."""
        self.device_manager = DeviceManager()
        self.device = self.device_manager.get_best_device()
        print("Custom kernel processor initialized with: " + self.device.name)
    
    fn apply_sharpen_filter(self, image: Image) -> Image:
        """Apply sharpening filter using custom kernel."""
        let kernel = create_sharpen_kernel()
        let result = gpu_convolution(image, kernel, 3)
        kernel.free()
        return result
    
    fn apply_emboss_filter(self, image: Image) -> Image:
        """Apply emboss effect using custom kernel."""
        let kernel = create_emboss_kernel()
        let result = gpu_convolution(image, kernel, 3)
        kernel.free()
        return result
    
    fn apply_outline_filter(self, image: Image) -> Image:
        """Apply outline detection using custom kernel."""
        let kernel = create_outline_kernel()
        let result = gpu_convolution(image, kernel, 3)
        kernel.free()
        return result
    
    fn apply_ridge_detection(self, image: Image) -> Image:
        """Apply ridge detection using 5x5 custom kernel."""
        let kernel = create_ridge_detection_kernel()
        let result = gpu_convolution(image, kernel, 5)
        kernel.free()
        return result
    
    fn apply_custom_blur(self, image: Image, size: Int, strength: Float32) -> Image:
        """Apply custom blur with variable parameters."""
        let kernel = create_custom_blur_kernel(size, strength)
        let result = gpu_convolution(image, kernel, size)
        kernel.free()
        return result

fn demonstrate_kernel_creation():
    """Demonstrate creating and using custom kernels."""
    print("\nCustom Kernel Creation Demonstration")
    print("====================================")
    
    # Create test image
    let test_image = create_test_image(256, 256, ImageFormat.RGB, DeviceType.CPU)
    print("Created test image: 256x256")
    
    # Initialize processor
    var processor = CustomKernelProcessor()
    
    # Apply different custom kernels
    print("\nApplying sharpen filter...")
    let sharpened = processor.apply_sharpen_filter(test_image)
    print("Sharpen filter applied")
    
    print("\nApplying emboss filter...")
    let embossed = processor.apply_emboss_filter(test_image)
    print("Emboss filter applied")
    
    print("\nApplying outline filter...")
    let outlined = processor.apply_outline_filter(test_image)
    print("Outline filter applied")
    
    print("\nApplying ridge detection...")
    let ridges = processor.apply_ridge_detection(test_image)
    print("Ridge detection applied")
    
    # Test custom blur with different parameters
    print("\nTesting custom blur with different parameters...")
    let light_blur = processor.apply_custom_blur(test_image, 5, 0.5)
    let heavy_blur = processor.apply_custom_blur(test_image, 9, 2.0)
    print("Custom blur filters applied (light and heavy)")

fn benchmark_kernel_performance():
    """Benchmark performance of different kernel sizes and types."""
    print("\nKernel Performance Benchmark")
    print("============================")
    
    let test_image = create_test_image(512, 512, ImageFormat.RGB, DeviceType.CPU)
    var processor = CustomKernelProcessor()
    
    # Benchmark different kernel sizes
    let kernel_sizes = [3, 5, 7, 9, 11]
    
    for size_idx in range(len(kernel_sizes)):
        let size = kernel_sizes[size_idx]
        
        print("\nBenchmarking " + str(size) + "x" + str(size) + " kernel...")
        
        let start_time = time.now()
        let result = processor.apply_custom_blur(test_image, size, 1.0)
        let end_time = time.now()
        
        let processing_time = end_time - start_time
        print("Processing time: " + str(processing_time) + "ms")
        print("Pixels per second: " + str(Float64(test_image.width * test_image.height) / processing_time * 1000.0))
    
    # Benchmark different kernel types
    print("\nBenchmarking different kernel types...")
    
    let kernels = ["sharpen", "emboss", "outline", "ridge"]
    
    for kernel_idx in range(len(kernels)):
        let kernel_name = kernels[kernel_idx]
        
        print("\nBenchmarking " + kernel_name + " kernel...")
        let start_time = time.now()
        
        var result: Image
        if kernel_name == "sharpen":
            result = processor.apply_sharpen_filter(test_image)
        elif kernel_name == "emboss":
            result = processor.apply_emboss_filter(test_image)
        elif kernel_name == "outline":
            result = processor.apply_outline_filter(test_image)
        else:  # ridge
            result = processor.apply_ridge_detection(test_image)
        
        let end_time = time.now()
        let processing_time = end_time - start_time
        print("Processing time: " + str(processing_time) + "ms")

fn demonstrate_kernel_composition():
    """Demonstrate composing multiple kernels for complex effects."""
    print("\nKernel Composition Demonstration")
    print("================================")
    
    let test_image = create_test_image(256, 256, ImageFormat.RGB, DeviceType.CPU)
    var processor = CustomKernelProcessor()
    
    # Apply multiple kernels in sequence
    print("\nApplying kernel composition: blur -> sharpen -> emboss...")
    
    var composed_result = test_image
    composed_result = processor.apply_custom_blur(composed_result, 5, 1.0)
    print("Step 1: Blur applied")
    
    composed_result = processor.apply_sharpen_filter(composed_result)
    print("Step 2: Sharpen applied")
    
    composed_result = processor.apply_emboss_filter(composed_result)
    print("Step 3: Emboss applied")
    
    print("Kernel composition completed")
    
    # Compare with single-step processing
    print("\nComparing with individual operations...")
    let individual_blur = processor.apply_custom_blur(test_image, 5, 1.0)
    let individual_sharpen = processor.apply_sharpen_filter(test_image)
    let individual_emboss = processor.apply_emboss_filter(test_image)
    
    print("Individual operations completed for comparison")

fn main():
    """Main function demonstrating custom kernel usage."""
    print("Image GPU - Custom Kernels Example")
    print("==================================")
    
    # Demonstrate kernel creation and usage
    demonstrate_kernel_creation()
    
    # Benchmark kernel performance
    benchmark_kernel_performance()
    
    # Demonstrate kernel composition
    demonstrate_kernel_composition()
    
    print("\nCustom kernels example completed successfully!")
    print("This example shows how to create specialized image processing effects")
    print("using custom convolution kernels optimized for GPU execution.")

# Import required modules
from math import sqrt, exp
import time

# Run the example
if __name__ == "__main__":
    main()
