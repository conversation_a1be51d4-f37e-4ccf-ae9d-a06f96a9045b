"""
Performance Benchmark Example

This example benchmarks the performance of various image processing operations
comparing CPU vs GPU execution and measuring throughput.
"""

from image_gpu import Image
from image_gpu.filters import gaussian_blur, edge_detect
from image_gpu.core.operations import resize_image, convert_to_grayscale, adjust_brightness
from image_gpu.io.loader import create_test_image
from image_gpu.gpu.device import DeviceManager
from image_gpu.core.types import ImageFormat, DeviceType

struct BenchmarkResult:
    """Structure to hold benchmark results."""
    var operation_name: String
    var image_size: String
    var cpu_time: Float64
    var gpu_time: Float64
    var speedup: Float64
    var throughput: Float64
    
    fn __init__(inout self, operation: String, size: String, cpu_ms: Float64, gpu_ms: Float64, pixels: Int):
        self.operation_name = operation
        self.image_size = size
        self.cpu_time = cpu_ms
        self.gpu_time = gpu_ms
        self.speedup = cpu_ms / gpu_ms if gpu_ms > 0 else 0.0
        self.throughput = Float64(pixels) / gpu_ms * 1000.0 if gpu_ms > 0 else 0.0
    
    fn print_result(self):
        """Print benchmark result in formatted way."""
        print(self.operation_name + " (" + self.image_size + "):")
        print("  CPU Time: " + str(self.cpu_time) + "ms")
        print("  GPU Time: " + str(self.gpu_time) + "ms")
        print("  Speedup: " + str(self.speedup) + "x")
        print("  Throughput: " + str(self.throughput / 1000000.0) + " Mpixels/sec")

struct PerformanceBenchmark:
    """Main benchmark runner."""
    var device_manager: DeviceManager
    var results: List[BenchmarkResult]
    
    fn __init__(inout self):
        """Initialize the benchmark runner."""
        self.device_manager = DeviceManager()
        self.results = List[BenchmarkResult]()
        print("Performance Benchmark initialized")
        print("Available devices:")
        for i in range(self.device_manager.get_device_count()):
            let device = self.device_manager.get_device(i)
            print("  " + str(i) + ": " + device.name + " (" + str(device.memory_size // (1024*1024)) + "MB)")
    
    fn benchmark_operation(inout self, operation_name: String, image_width: Int, image_height: Int):
        """Benchmark a specific operation on both CPU and GPU."""
        let pixels = image_width * image_height
        let size_str = str(image_width) + "x" + str(image_height)
        
        print("\nBenchmarking " + operation_name + " on " + size_str + " image...")
        
        # Create test images for CPU and GPU
        var cpu_image = create_test_image(image_width, image_height, ImageFormat.RGB, DeviceType.CPU)
        var gpu_image = create_test_image(image_width, image_height, ImageFormat.RGB, DeviceType.CUDA)
        
        # Benchmark CPU execution
        let cpu_start = time.now()
        let cpu_result = self._execute_operation(cpu_image, operation_name)
        let cpu_time = time.now() - cpu_start
        
        # Benchmark GPU execution
        let gpu_start = time.now()
        let gpu_result = self._execute_operation(gpu_image, operation_name)
        let gpu_time = time.now() - gpu_start
        
        # Store result
        let result = BenchmarkResult(operation_name, size_str, cpu_time, gpu_time, pixels)
        self.results.append(result)
        result.print_result()
    
    fn _execute_operation(self, image: Image, operation: String) -> Image:
        """Execute a specific operation on an image."""
        if operation == "gaussian_blur":
            return gaussian_blur(image, sigma=2.0)
        elif operation == "edge_detect":
            return edge_detect(image, method="sobel")
        elif operation == "resize":
            return resize_image(image, image.width // 2, image.height // 2)
        elif operation == "grayscale":
            return convert_to_grayscale(image)
        elif operation == "brightness":
            return adjust_brightness(image, 1.5)
        else:
            return image
    
    fn run_comprehensive_benchmark(inout self):
        """Run comprehensive benchmark across different operations and sizes."""
        let operations = ["gaussian_blur", "edge_detect", "resize", "grayscale", "brightness"]
        let sizes = [(256, 256), (512, 512), (1024, 1024), (2048, 2048)]
        
        print("Running comprehensive benchmark...")
        print("Operations: " + str(len(operations)))
        print("Image sizes: " + str(len(sizes)))
        print("Total tests: " + str(len(operations) * len(sizes)))
        
        for op_idx in range(len(operations)):
            let operation = operations[op_idx]
            for size_idx in range(len(sizes)):
                let (width, height) = sizes[size_idx]
                self.benchmark_operation(operation, width, height)
    
    fn print_summary(self):
        """Print benchmark summary."""
        print("\n" + "="*60)
        print("BENCHMARK SUMMARY")
        print("="*60)
        
        var total_speedup: Float64 = 0.0
        var max_speedup: Float64 = 0.0
        var min_speedup: Float64 = 1000.0
        var max_throughput: Float64 = 0.0
        
        for i in range(len(self.results)):
            let result = self.results[i]
            total_speedup += result.speedup
            max_speedup = max(max_speedup, result.speedup)
            min_speedup = min(min_speedup, result.speedup)
            max_throughput = max(max_throughput, result.throughput)
        
        let avg_speedup = total_speedup / Float64(len(self.results))
        
        print("Average GPU Speedup: " + str(avg_speedup) + "x")
        print("Maximum GPU Speedup: " + str(max_speedup) + "x")
        print("Minimum GPU Speedup: " + str(min_speedup) + "x")
        print("Peak Throughput: " + str(max_throughput / 1000000.0) + " Mpixels/sec")
        print("Total Tests: " + str(len(self.results)))

fn benchmark_memory_usage():
    """Benchmark memory usage for different image sizes."""
    print("\n" + "="*60)
    print("MEMORY USAGE BENCHMARK")
    print("="*60)
    
    let sizes = [(128, 128), (256, 256), (512, 512), (1024, 1024), (2048, 2048), (4096, 4096)]
    
    for size_idx in range(len(sizes)):
        let (width, height) = sizes[size_idx]
        let test_image = create_test_image(width, height, ImageFormat.RGB, DeviceType.CPU)
        
        let memory_bytes = test_image.size_bytes()
        let memory_mb = Float64(memory_bytes) / (1024.0 * 1024.0)
        
        print(str(width) + "x" + str(height) + " RGB image: " + str(memory_mb) + " MB")
        
        # Test RGBA format
        let rgba_image = create_test_image(width, height, ImageFormat.RGBA, DeviceType.CPU)
        let rgba_memory_mb = Float64(rgba_image.size_bytes()) / (1024.0 * 1024.0)
        print(str(width) + "x" + str(height) + " RGBA image: " + str(rgba_memory_mb) + " MB")

fn benchmark_batch_sizes():
    """Benchmark different batch sizes for processing."""
    print("\n" + "="*60)
    print("BATCH SIZE BENCHMARK")
    print("="*60)
    
    let image_size = (256, 256)
    let batch_sizes = [1, 4, 8, 16, 32, 64]
    
    for batch_idx in range(len(batch_sizes)):
        let batch_size = batch_sizes[batch_idx]
        
        print("\nTesting batch size: " + str(batch_size))
        
        # Create batch of images
        var images = List[Image]()
        for i in range(batch_size):
            let img = create_test_image(image_size.0, image_size.1, ImageFormat.RGB, DeviceType.CUDA)
            images.append(img)
        
        # Benchmark batch processing
        let start_time = time.now()
        
        for i in range(len(images)):
            let processed = gaussian_blur(images[i], sigma=1.5)
        
        let end_time = time.now()
        let total_time = end_time - start_time
        let time_per_image = total_time / Float64(batch_size)
        let throughput = Float64(batch_size) / total_time * 1000.0
        
        print("Total time: " + str(total_time) + "ms")
        print("Time per image: " + str(time_per_image) + "ms")
        print("Throughput: " + str(throughput) + " images/sec")

fn stress_test():
    """Perform stress test with large images and intensive operations."""
    print("\n" + "="*60)
    print("STRESS TEST")
    print("="*60)
    
    # Test with very large image
    print("Creating large test image (4096x4096)...")
    let large_image = create_test_image(4096, 4096, ImageFormat.RGB, DeviceType.CUDA)
    print("Large image created, memory usage: " + str(large_image.size_bytes() / (1024*1024)) + " MB")
    
    # Apply intensive processing pipeline
    print("\nApplying intensive processing pipeline...")
    let pipeline_start = time.now()
    
    var processed = large_image
    processed = gaussian_blur(processed, sigma=3.0)
    print("Step 1/5: Gaussian blur completed")
    
    processed = edge_detect(processed, method="sobel")
    print("Step 2/5: Edge detection completed")
    
    processed = resize_image(processed, 2048, 2048)
    print("Step 3/5: Resize completed")
    
    processed = adjust_brightness(processed, 1.3)
    print("Step 4/5: Brightness adjustment completed")
    
    processed = convert_to_grayscale(processed)
    print("Step 5/5: Grayscale conversion completed")
    
    let pipeline_time = time.now() - pipeline_start
    print("Intensive pipeline completed in " + str(pipeline_time) + "ms")
    print("Final image size: " + str(processed.width) + "x" + str(processed.height))

fn main():
    """Main function running all benchmarks."""
    print("Image GPU - Performance Benchmark")
    print("=================================")
    
    # Initialize and run comprehensive benchmark
    var benchmark = PerformanceBenchmark()
    benchmark.run_comprehensive_benchmark()
    benchmark.print_summary()
    
    # Run additional benchmarks
    benchmark_memory_usage()
    benchmark_batch_sizes()
    stress_test()
    
    print("\n" + "="*60)
    print("BENCHMARK COMPLETED")
    print("="*60)
    print("All performance tests completed successfully!")
    print("Results show the performance characteristics of GPU-accelerated image processing.")

# Import required modules
import time

# Run the benchmark
if __name__ == "__main__":
    main()
