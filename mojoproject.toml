[project]
name = "image-gpu"
version = "0.1.0"
description = "GPU-accelerated image manipulation library for Mojo"
authors = ["Your Name <<EMAIL>>"]
license = "MIT"
readme = "README.md"

[dependencies]
# Add Mojo dependencies here as they become available

[build]
# Build configuration
optimization = "release"
target = "gpu"

[dev-dependencies]
# Development dependencies for testing

[features]
default = ["gpu", "cuda"]
gpu = []
cuda = []
opencl = []
metal = []

[package]
include = [
    "src/**/*.mojo",
    "src/**/*.🔥",
    "README.md",
    "LICENSE",
]
exclude = [
    "tests/",
    "examples/",
    "docs/",
    "*.tmp",
    "*.log",
]
