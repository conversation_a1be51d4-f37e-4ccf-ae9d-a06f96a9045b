"""
Core image data structures and pixel manipulation utilities.
"""

from memory import Unsafe<PERSON><PERSON><PERSON>, memset_zero, memcpy
from utils import Variant
from .types import ImageFormat, DataType, ColorSpace, DeviceType

# Pixel structure for individual pixel operations
@value
struct Pixel:
    """Represents a single pixel with multiple channels."""
    var r: Float32
    var g: Float32
    var b: Float32
    var a: Float32
    
    fn __init__(inout self, r: Float32 = 0.0, g: Float32 = 0.0, b: Float32 = 0.0, a: Float32 = 1.0):
        self.r = r
        self.g = g
        self.b = b
        self.a = a
    
    fn __init__(inout self, gray: Float32):
        """Create a grayscale pixel."""
        self.r = gray
        self.g = gray
        self.b = gray
        self.a = 1.0
    
    fn luminance(self) -> Float32:
        """Calculate the luminance of the pixel using standard weights."""
        return 0.299 * self.r + 0.587 * self.g + 0.114 * self.b
    
    fn to_grayscale(self) -> Pixel:
        """Convert pixel to grayscale."""
        let gray = self.luminance()
        return Pixel(gray, gray, gray, self.a)
    
    fn clamp(inout self):
        """Clamp pixel values to [0.0, 1.0] range."""
        self.r = max(0.0, min(1.0, self.r))
        self.g = max(0.0, min(1.0, self.g))
        self.b = max(0.0, min(1.0, self.b))
        self.a = max(0.0, min(1.0, self.a))

# Main Image structure
struct Image:
    """
    Main image data structure supporting GPU acceleration.
    
    This structure holds image data and metadata, with support for
    different pixel formats, data types, and GPU memory management.
    """
    var width: Int
    var height: Int
    var channels: Int
    var format: ImageFormat
    var data_type: DataType
    var color_space: ColorSpace
    var device_type: DeviceType
    var data: UnsafePointer[UInt8]
    var owns_data: Bool
    
    fn __init__(inout self, width: Int, height: Int, format: ImageFormat = ImageFormat.RGB, 
                data_type: DataType = DataType.UINT8, color_space: ColorSpace = ColorSpace.SRGB,
                device_type: DeviceType = DeviceType.CPU):
        """Initialize a new image with the specified dimensions and format."""
        self.width = width
        self.height = height
        self.format = format
        self.channels = format.channels()
        self.data_type = data_type
        self.color_space = color_space
        self.device_type = device_type
        self.owns_data = True
        
        # Allocate memory for image data
        let total_size = width * height * self.channels * data_type.size_bytes()
        self.data = UnsafePointer[UInt8].alloc(total_size)
        memset_zero(self.data, total_size)
    
    fn __init__(inout self, width: Int, height: Int, data: UnsafePointer[UInt8], 
                format: ImageFormat = ImageFormat.RGB, data_type: DataType = DataType.UINT8,
                color_space: ColorSpace = ColorSpace.SRGB, device_type: DeviceType = DeviceType.CPU):
        """Initialize an image with existing data (does not take ownership)."""
        self.width = width
        self.height = height
        self.format = format
        self.channels = format.channels()
        self.data_type = data_type
        self.color_space = color_space
        self.device_type = device_type
        self.data = data
        self.owns_data = False
    
    fn __del__(owned self):
        """Clean up allocated memory."""
        if self.owns_data:
            self.data.free()
    
    fn __copyinit__(inout self, existing: Self):
        """Create a deep copy of an existing image."""
        self.width = existing.width
        self.height = existing.height
        self.channels = existing.channels
        self.format = existing.format
        self.data_type = existing.data_type
        self.color_space = existing.color_space
        self.device_type = existing.device_type
        self.owns_data = True
        
        let total_size = self.width * self.height * self.channels * self.data_type.size_bytes()
        self.data = UnsafePointer[UInt8].alloc(total_size)
        memcpy(self.data, existing.data, total_size)
    
    fn size_bytes(self) -> Int:
        """Return the total size of image data in bytes."""
        return self.width * self.height * self.channels * self.data_type.size_bytes()
    
    fn get_pixel_offset(self, x: Int, y: Int) -> Int:
        """Calculate the byte offset for a pixel at (x, y)."""
        return (y * self.width + x) * self.channels * self.data_type.size_bytes()
    
    fn is_valid_coords(self, x: Int, y: Int) -> Bool:
        """Check if coordinates are within image bounds."""
        return x >= 0 and x < self.width and y >= 0 and y < self.height
    
    fn get_pixel_uint8(self, x: Int, y: Int) -> Pixel:
        """Get a pixel value as normalized floats (assumes UINT8 data)."""
        if not self.is_valid_coords(x, y) or self.data_type != DataType.UINT8:
            return Pixel()
        
        let offset = self.get_pixel_offset(x, y)
        let ptr = self.data + offset
        
        if self.channels == 1:
            let gray = Float32(ptr[0]) / 255.0
            return Pixel(gray)
        elif self.channels == 3:
            let r = Float32(ptr[0]) / 255.0
            let g = Float32(ptr[1]) / 255.0
            let b = Float32(ptr[2]) / 255.0
            return Pixel(r, g, b)
        elif self.channels == 4:
            let r = Float32(ptr[0]) / 255.0
            let g = Float32(ptr[1]) / 255.0
            let b = Float32(ptr[2]) / 255.0
            let a = Float32(ptr[3]) / 255.0
            return Pixel(r, g, b, a)
        else:
            return Pixel()
    
    fn set_pixel_uint8(inout self, x: Int, y: Int, pixel: Pixel):
        """Set a pixel value from normalized floats (assumes UINT8 data)."""
        if not self.is_valid_coords(x, y) or self.data_type != DataType.UINT8:
            return
        
        let offset = self.get_pixel_offset(x, y)
        let ptr = self.data + offset
        
        if self.channels == 1:
            ptr[0] = UInt8(pixel.luminance() * 255.0)
        elif self.channels == 3:
            ptr[0] = UInt8(pixel.r * 255.0)
            ptr[1] = UInt8(pixel.g * 255.0)
            ptr[2] = UInt8(pixel.b * 255.0)
        elif self.channels == 4:
            ptr[0] = UInt8(pixel.r * 255.0)
            ptr[1] = UInt8(pixel.g * 255.0)
            ptr[2] = UInt8(pixel.b * 255.0)
            ptr[3] = UInt8(pixel.a * 255.0)
    
    fn fill(inout self, pixel: Pixel):
        """Fill the entire image with a single pixel value."""
        for y in range(self.height):
            for x in range(self.width):
                self.set_pixel_uint8(x, y, pixel)
    
    fn clone(self) -> Image:
        """Create a deep copy of this image."""
        return Image(self)
