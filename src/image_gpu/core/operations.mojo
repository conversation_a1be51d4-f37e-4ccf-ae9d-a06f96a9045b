"""
Basic image operations like resize, crop, rotate, etc.

This module provides fundamental image manipulation operations
that can be accelerated on GPU when available.
"""

from math import sqrt, sin, cos, pi
from ..core.image import Image, Pixel
from ..core.types import ImageFormat, DataType, DeviceType
from ..gpu.memory import GPUBuffer

fn resize_image(image: Image, new_width: Int, new_height: Int, 
               interpolation: String = "bilinear") -> Image:
    """
    Resize an image to new dimensions.
    
    Args:
        image: Source image
        new_width: Target width
        new_height: Target height
        interpolation: Interpolation method ("nearest", "bilinear", "bicubic")
    
    Returns:
        Resized image
    """
    var result = Image(new_width, new_height, image.format, image.data_type, 
                      image.color_space, image.device_type)
    
    if interpolation == "nearest":
        _resize_nearest_neighbor(image, result)
    elif interpolation == "bilinear":
        _resize_bilinear(image, result)
    elif interpolation == "bicubic":
        _resize_bicubic(image, result)
    else:
        # Default to bilinear
        _resize_bilinear(image, result)
    
    return result

fn _resize_nearest_neighbor(source: Image, inout target: Image):
    """Resize using nearest neighbor interpolation."""
    let x_ratio = Float32(source.width) / Float32(target.width)
    let y_ratio = Float32(source.height) / Float32(target.height)
    
    for y in range(target.height):
        for x in range(target.width):
            let src_x = Int(Float32(x) * x_ratio)
            let src_y = Int(Float32(y) * y_ratio)
            
            let pixel = source.get_pixel_uint8(src_x, src_y)
            target.set_pixel_uint8(x, y, pixel)

fn _resize_bilinear(source: Image, inout target: Image):
    """Resize using bilinear interpolation."""
    let x_ratio = Float32(source.width - 1) / Float32(target.width)
    let y_ratio = Float32(source.height - 1) / Float32(target.height)
    
    for y in range(target.height):
        for x in range(target.width):
            let gx = Float32(x) * x_ratio
            let gy = Float32(y) * y_ratio
            
            let gxi = Int(gx)
            let gyi = Int(gy)
            
            let dx = gx - Float32(gxi)
            let dy = gy - Float32(gyi)
            
            # Get four neighboring pixels
            let p1 = source.get_pixel_uint8(gxi, gyi)
            let p2 = source.get_pixel_uint8(min(gxi + 1, source.width - 1), gyi)
            let p3 = source.get_pixel_uint8(gxi, min(gyi + 1, source.height - 1))
            let p4 = source.get_pixel_uint8(min(gxi + 1, source.width - 1), min(gyi + 1, source.height - 1))
            
            # Bilinear interpolation
            let r = p1.r * (1 - dx) * (1 - dy) + p2.r * dx * (1 - dy) + p3.r * (1 - dx) * dy + p4.r * dx * dy
            let g = p1.g * (1 - dx) * (1 - dy) + p2.g * dx * (1 - dy) + p3.g * (1 - dx) * dy + p4.g * dx * dy
            let b = p1.b * (1 - dx) * (1 - dy) + p2.b * dx * (1 - dy) + p3.b * (1 - dx) * dy + p4.b * dx * dy
            let a = p1.a * (1 - dx) * (1 - dy) + p2.a * dx * (1 - dy) + p3.a * (1 - dx) * dy + p4.a * dx * dy
            
            target.set_pixel_uint8(x, y, Pixel(r, g, b, a))

fn _resize_bicubic(source: Image, inout target: Image):
    """Resize using bicubic interpolation (simplified version)."""
    # For now, fall back to bilinear
    # TODO: Implement proper bicubic interpolation
    _resize_bilinear(source, target)

fn crop_image(image: Image, x: Int, y: Int, width: Int, height: Int) -> Image:
    """
    Crop a rectangular region from an image.
    
    Args:
        image: Source image
        x, y: Top-left corner of crop region
        width, height: Dimensions of crop region
    
    Returns:
        Cropped image
    """
    # Clamp crop region to image bounds
    let crop_x = max(0, min(x, image.width))
    let crop_y = max(0, min(y, image.height))
    let crop_width = max(0, min(width, image.width - crop_x))
    let crop_height = max(0, min(height, image.height - crop_y))
    
    var result = Image(crop_width, crop_height, image.format, image.data_type,
                      image.color_space, image.device_type)
    
    for dy in range(crop_height):
        for dx in range(crop_width):
            let src_x = crop_x + dx
            let src_y = crop_y + dy
            let pixel = image.get_pixel_uint8(src_x, src_y)
            result.set_pixel_uint8(dx, dy, pixel)
    
    return result

fn rotate_image(image: Image, angle_degrees: Float32) -> Image:
    """
    Rotate an image by the specified angle.
    
    Args:
        image: Source image
        angle_degrees: Rotation angle in degrees (positive = clockwise)
    
    Returns:
        Rotated image
    """
    let angle_rad = angle_degrees * pi / 180.0
    let cos_a = cos(angle_rad)
    let sin_a = sin(angle_rad)
    
    # Calculate new image dimensions
    let old_w = Float32(image.width)
    let old_h = Float32(image.height)
    
    let new_w = Int(abs(old_w * cos_a) + abs(old_h * sin_a))
    let new_h = Int(abs(old_w * sin_a) + abs(old_h * cos_a))
    
    var result = Image(new_w, new_h, image.format, image.data_type,
                      image.color_space, image.device_type)
    
    let center_x = Float32(new_w) / 2.0
    let center_y = Float32(new_h) / 2.0
    let old_center_x = old_w / 2.0
    let old_center_y = old_h / 2.0
    
    for y in range(new_h):
        for x in range(new_w):
            # Transform coordinates back to original image
            let dx = Float32(x) - center_x
            let dy = Float32(y) - center_y
            
            let src_x = Int(dx * cos_a + dy * sin_a + old_center_x)
            let src_y = Int(-dx * sin_a + dy * cos_a + old_center_y)
            
            if image.is_valid_coords(src_x, src_y):
                let pixel = image.get_pixel_uint8(src_x, src_y)
                result.set_pixel_uint8(x, y, pixel)
            else:
                # Fill with transparent/black pixel
                result.set_pixel_uint8(x, y, Pixel(0.0, 0.0, 0.0, 0.0))
    
    return result

fn flip_horizontal(image: Image) -> Image:
    """
    Flip an image horizontally (mirror left-right).
    
    Args:
        image: Source image
    
    Returns:
        Horizontally flipped image
    """
    var result = Image(image.width, image.height, image.format, image.data_type,
                      image.color_space, image.device_type)
    
    for y in range(image.height):
        for x in range(image.width):
            let src_x = image.width - 1 - x
            let pixel = image.get_pixel_uint8(src_x, y)
            result.set_pixel_uint8(x, y, pixel)
    
    return result

fn flip_vertical(image: Image) -> Image:
    """
    Flip an image vertically (mirror top-bottom).
    
    Args:
        image: Source image
    
    Returns:
        Vertically flipped image
    """
    var result = Image(image.width, image.height, image.format, image.data_type,
                      image.color_space, image.device_type)
    
    for y in range(image.height):
        for x in range(image.width):
            let src_y = image.height - 1 - y
            let pixel = image.get_pixel_uint8(x, src_y)
            result.set_pixel_uint8(x, y, pixel)
    
    return result

fn convert_to_grayscale(image: Image) -> Image:
    """
    Convert an image to grayscale.
    
    Args:
        image: Source image
    
    Returns:
        Grayscale image
    """
    var result = Image(image.width, image.height, ImageFormat.GRAYSCALE, image.data_type,
                      image.color_space, image.device_type)
    
    for y in range(image.height):
        for x in range(image.width):
            let pixel = image.get_pixel_uint8(x, y)
            let gray_pixel = pixel.to_grayscale()
            result.set_pixel_uint8(x, y, gray_pixel)
    
    return result

fn adjust_brightness(image: Image, factor: Float32) -> Image:
    """
    Adjust image brightness.
    
    Args:
        image: Source image
        factor: Brightness factor (1.0 = no change, >1.0 = brighter, <1.0 = darker)
    
    Returns:
        Brightness-adjusted image
    """
    var result = image.clone()
    
    for y in range(image.height):
        for x in range(image.width):
            var pixel = image.get_pixel_uint8(x, y)
            pixel.r = min(1.0, pixel.r * factor)
            pixel.g = min(1.0, pixel.g * factor)
            pixel.b = min(1.0, pixel.b * factor)
            pixel.clamp()
            result.set_pixel_uint8(x, y, pixel)
    
    return result

fn adjust_contrast(image: Image, factor: Float32) -> Image:
    """
    Adjust image contrast.
    
    Args:
        image: Source image
        factor: Contrast factor (1.0 = no change, >1.0 = more contrast, <1.0 = less contrast)
    
    Returns:
        Contrast-adjusted image
    """
    var result = image.clone()
    
    for y in range(image.height):
        for x in range(image.width):
            var pixel = image.get_pixel_uint8(x, y)
            pixel.r = (pixel.r - 0.5) * factor + 0.5
            pixel.g = (pixel.g - 0.5) * factor + 0.5
            pixel.b = (pixel.b - 0.5) * factor + 0.5
            pixel.clamp()
            result.set_pixel_uint8(x, y, pixel)
    
    return result
