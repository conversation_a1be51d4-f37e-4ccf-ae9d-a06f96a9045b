"""
Core type definitions for the image-gpu library.
"""

from memory import UnsafePointer
from utils import Variant

# Image format enumeration
@value
struct ImageFormat:
    """Supported image formats."""
    var value: Int
    
    alias UNKNOWN = ImageFormat(0)
    alias RGB = ImageFormat(1)
    alias RGBA = ImageFormat(2)
    alias GRAYSCALE = ImageFormat(3)
    alias BGR = ImageFormat(4)
    alias BGRA = ImageFormat(5)
    
    fn __init__(inout self, value: Int):
        self.value = value
    
    fn __eq__(self, other: ImageFormat) -> Bool:
        return self.value == other.value
    
    fn __ne__(self, other: ImageFormat) -> Bool:
        return self.value != other.value
    
    fn channels(self) -> Int:
        """Return the number of channels for this format."""
        if self == ImageFormat.RGB or self == ImageFormat.BGR:
            return 3
        elif self == ImageFormat.RGBA or self == ImageFormat.BGRA:
            return 4
        elif self == ImageFormat.GRAYSCALE:
            return 1
        else:
            return 0

# Data type enumeration for pixel values
@value
struct DataType:
    """Supported data types for pixel values."""
    var value: Int
    
    alias UINT8 = DataType(0)
    alias UINT16 = DataType(1)
    alias FLOAT32 = DataType(2)
    alias FLOAT64 = DataType(3)
    
    fn __init__(inout self, value: Int):
        self.value = value
    
    fn __eq__(self, other: DataType) -> Bool:
        return self.value == other.value
    
    fn __ne__(self, other: DataType) -> Bool:
        return self.value != other.value
    
    fn size_bytes(self) -> Int:
        """Return the size in bytes for this data type."""
        if self == DataType.UINT8:
            return 1
        elif self == DataType.UINT16:
            return 2
        elif self == DataType.FLOAT32:
            return 4
        elif self == DataType.FLOAT64:
            return 8
        else:
            return 0

# Color space definitions
@value
struct ColorSpace:
    """Color space definitions for image processing."""
    var value: Int
    
    alias SRGB = ColorSpace(0)
    alias LINEAR_RGB = ColorSpace(1)
    alias HSV = ColorSpace(2)
    alias HSL = ColorSpace(3)
    alias LAB = ColorSpace(4)
    alias XYZ = ColorSpace(5)
    
    fn __init__(inout self, value: Int):
        self.value = value
    
    fn __eq__(self, other: ColorSpace) -> Bool:
        return self.value == other.value
    
    fn __ne__(self, other: ColorSpace) -> Bool:
        return self.value != other.value

# GPU device types
@value
struct DeviceType:
    """GPU device types supported."""
    var value: Int
    
    alias CPU = DeviceType(0)
    alias CUDA = DeviceType(1)
    alias OPENCL = DeviceType(2)
    alias METAL = DeviceType(3)
    
    fn __init__(inout self, value: Int):
        self.value = value
    
    fn __eq__(self, other: DeviceType) -> Bool:
        return self.value == other.value
    
    fn __ne__(self, other: DeviceType) -> Bool:
        return self.value != other.value
