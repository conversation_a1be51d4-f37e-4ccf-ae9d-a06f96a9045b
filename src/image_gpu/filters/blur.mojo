"""
Blur filters for image processing.

This module provides various blur effects including Gaussian blur,
box blur, and motion blur with GPU acceleration.
"""

from ..core.image import Image
from ..gpu.kernels import gpu_gaussian_blur, create_gaussian_kernel, ConvolutionKernel
from ..gpu.device import <PERSON><PERSON><PERSON>anager
from ..gpu.memory import GPUBuffer

fn gaussian_blur(image: Image, sigma: Float32, kernel_size: Int = 0) -> Image:
    """
    Apply Gaussian blur to an image.
    
    Args:
        image: Input image
        sigma: Standard deviation for Gaussian kernel
        kernel_size: Size of the kernel (auto-calculated if 0)
    
    Returns:
        Blurred image
    """
    var actual_kernel_size = kernel_size
    if actual_kernel_size == 0:
        # Auto-calculate kernel size based on sigma
        actual_kernel_size = Int(sigma * 6.0) + 1
        if actual_kernel_size % 2 == 0:
            actual_kernel_size += 1  # Ensure odd size
    
    return gpu_gaussian_blur(image, sigma, actual_kernel_size)

fn box_blur(image: Image, radius: Int) -> Image:
    """
    Apply box blur (uniform blur) to an image.
    
    Args:
        image: Input image
        radius: Blur radius
    
    Returns:
        Blurred image
    """
    let kernel_size = radius * 2 + 1
    let kernel_data = _create_box_kernel(kernel_size)
    
    let device_manager = DeviceManager()
    let device = device_manager.get_best_device()
    let conv_kernel = ConvolutionKernel(kernel_size, kernel_data, device)
    
    # Create GPU buffers
    let input_size = image.size_bytes()
    var input_buffer = GPUBuffer(image.data, input_size, device.device_type)
    var output_buffer = GPUBuffer(input_size, device.device_type)
    
    # Execute kernel
    conv_kernel.execute(input_buffer, output_buffer, image.width, image.height, image.channels)
    
    # Create result image
    var result = Image(image.width, image.height, image.format, image.data_type,
                      image.color_space, device.device_type)
    
    # Copy result back
    output_buffer.copy_to_host()
    from memory import memcpy
    memcpy(result.data, output_buffer.get_host_ptr(), input_size)
    
    # Cleanup
    kernel_data.free()
    
    return result

fn motion_blur(image: Image, length: Int, angle: Float32) -> Image:
    """
    Apply motion blur to an image.
    
    Args:
        image: Input image
        length: Length of motion blur
        angle: Angle of motion in degrees
    
    Returns:
        Motion blurred image
    """
    let kernel_data = _create_motion_kernel(length, angle)
    let kernel_size = length
    
    let device_manager = DeviceManager()
    let device = device_manager.get_best_device()
    let conv_kernel = ConvolutionKernel(kernel_size, kernel_data, device)
    
    # Create GPU buffers
    let input_size = image.size_bytes()
    var input_buffer = GPUBuffer(image.data, input_size, device.device_type)
    var output_buffer = GPUBuffer(input_size, device.device_type)
    
    # Execute kernel
    conv_kernel.execute(input_buffer, output_buffer, image.width, image.height, image.channels)
    
    # Create result image
    var result = Image(image.width, image.height, image.format, image.data_type,
                      image.color_space, device.device_type)
    
    # Copy result back
    output_buffer.copy_to_host()
    from memory import memcpy
    memcpy(result.data, output_buffer.get_host_ptr(), input_size)
    
    # Cleanup
    kernel_data.free()
    
    return result

fn radial_blur(image: Image, center_x: Float32, center_y: Float32, strength: Float32) -> Image:
    """
    Apply radial blur (zoom blur) to an image.
    
    Args:
        image: Input image
        center_x: X coordinate of blur center (0.0 to 1.0)
        center_y: Y coordinate of blur center (0.0 to 1.0)
        strength: Blur strength
    
    Returns:
        Radially blurred image
    """
    var result = image.clone()
    
    let cx = Int(center_x * Float32(image.width))
    let cy = Int(center_y * Float32(image.height))
    
    # Apply radial blur using CPU implementation
    # TODO: Implement GPU version
    for y in range(image.height):
        for x in range(image.width):
            let dx = Float32(x - cx)
            let dy = Float32(y - cy)
            let distance = sqrt(dx * dx + dy * dy)
            
            if distance > 0:
                let samples = Int(strength * distance / 10.0) + 1
                var r_sum: Float32 = 0.0
                var g_sum: Float32 = 0.0
                var b_sum: Float32 = 0.0
                var a_sum: Float32 = 0.0
                
                for i in range(samples):
                    let t = Float32(i) / Float32(samples)
                    let sample_x = Int(Float32(cx) + dx * t)
                    let sample_y = Int(Float32(cy) + dy * t)
                    
                    if image.is_valid_coords(sample_x, sample_y):
                        let pixel = image.get_pixel_uint8(sample_x, sample_y)
                        r_sum += pixel.r
                        g_sum += pixel.g
                        b_sum += pixel.b
                        a_sum += pixel.a
                
                from ..core.image import Pixel
                let avg_pixel = Pixel(r_sum / Float32(samples), g_sum / Float32(samples),
                                    b_sum / Float32(samples), a_sum / Float32(samples))
                result.set_pixel_uint8(x, y, avg_pixel)
    
    return result

fn _create_box_kernel(size: Int) -> UnsafePointer[Float32]:
    """Create a box blur kernel."""
    let kernel = UnsafePointer[Float32].alloc(size * size)
    let value = 1.0 / Float32(size * size)
    
    for i in range(size * size):
        kernel[i] = value
    
    return kernel

fn _create_motion_kernel(length: Int, angle_degrees: Float32) -> UnsafePointer[Float32]:
    """Create a motion blur kernel."""
    let kernel = UnsafePointer[Float32].alloc(length * length)
    
    # Initialize kernel to zero
    for i in range(length * length):
        kernel[i] = 0.0
    
    # Calculate motion direction
    from math import cos, sin, pi
    let angle_rad = angle_degrees * pi / 180.0
    let dx = cos(angle_rad)
    let dy = sin(angle_rad)
    
    let half_length = length // 2
    let center = half_length
    
    # Draw line in kernel
    for i in range(length):
        let t = Float32(i - half_length)
        let x = Int(Float32(center) + t * dx)
        let y = Int(Float32(center) + t * dy)
        
        if x >= 0 and x < length and y >= 0 and y < length:
            kernel[y * length + x] = 1.0 / Float32(length)
    
    return kernel

# Helper functions
from math import sqrt
