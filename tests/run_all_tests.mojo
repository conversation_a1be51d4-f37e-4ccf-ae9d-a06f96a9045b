"""
Test runner for all image-gpu tests.

This script runs all test suites and provides a comprehensive
test report for the image-gpu library.
"""

from test_core import run_core_tests
from test_operations import run_operations_tests

fn run_filter_tests():
    """Run filter tests (placeholder)."""
    print("Running Filter Tests")
    print("====================")
    print("Filter tests would be implemented here")
    print("Testing gaussian_blur, edge_detect, etc.")
    print("Filter tests: PLACEHOLDER")

fn run_gpu_tests():
    """Run GPU-specific tests (placeholder)."""
    print("Running GPU Tests")
    print("=================")
    print("GPU tests would be implemented here")
    print("Testing device detection, memory management, kernel execution")
    print("GPU tests: PLACEHOLDER")

fn run_io_tests():
    """Run I/O tests (placeholder)."""
    print("Running I/O Tests")
    print("=================")
    print("I/O tests would be implemented here")
    print("Testing image loading, saving, format detection")
    print("I/O tests: PLACEHOLDER")

fn run_performance_tests():
    """Run performance tests."""
    print("Running Performance Tests")
    print("=========================")
    
    # Import performance benchmark
    from examples.performance_benchmark import main as run_benchmark
    
    print("Performance tests integrated with benchmark example")
    print("See examples/performance_benchmark.mojo for detailed performance testing")
    print("Performance tests: INTEGRATED")

fn print_test_header():
    """Print test suite header."""
    print("=" * 60)
    print("IMAGE-GPU LIBRARY TEST SUITE")
    print("=" * 60)
    print("Running comprehensive tests for the image-gpu library")
    print("This includes core functionality, operations, filters, GPU, and I/O")
    print("")

fn print_test_footer(total_suites: Int, passed_suites: Int):
    """Print test suite footer."""
    print("")
    print("=" * 60)
    print("TEST SUITE SUMMARY")
    print("=" * 60)
    print("Total test suites: " + str(total_suites))
    print("Passed test suites: " + str(passed_suites))
    print("Failed test suites: " + str(total_suites - passed_suites))
    
    if passed_suites == total_suites:
        print("🎉 ALL TESTS PASSED! 🎉")
        print("The image-gpu library is working correctly.")
    else:
        print("⚠️  SOME TESTS FAILED ⚠️")
        print("Please review the failed tests above.")
    
    print("=" * 60)

fn run_integration_tests():
    """Run integration tests."""
    print("Running Integration Tests")
    print("=========================")
    
    # Test that can create and process images end-to-end
    print("Testing end-to-end image processing pipeline...")
    
    from image_gpu.io.loader import create_test_image
    from image_gpu.filters import gaussian_blur
    from image_gpu.core.operations import resize_image
    from image_gpu.core.types import ImageFormat, DeviceType
    
    try:
        # Create test image
        let original = create_test_image(100, 100, ImageFormat.RGB, DeviceType.CPU)
        print("✓ Image creation successful")
        
        # Apply filter
        let blurred = gaussian_blur(original, sigma=1.0)
        print("✓ Gaussian blur filter successful")
        
        # Apply operation
        let resized = resize_image(blurred, 50, 50, "bilinear")
        print("✓ Resize operation successful")
        
        # Verify final result
        if resized.width == 50 and resized.height == 50:
            print("✓ Integration test PASSED")
            return True
        else:
            print("✗ Integration test FAILED - incorrect dimensions")
            return False
            
    except:
        print("✗ Integration test FAILED - exception occurred")
        return False

fn run_memory_tests():
    """Run memory management tests."""
    print("Running Memory Tests")
    print("====================")
    
    print("Testing memory allocation and deallocation...")
    
    from image_gpu.core.image import Image
    from image_gpu.core.types import ImageFormat, DataType, DeviceType
    
    # Test creating and destroying multiple images
    var images = List[Image]()
    
    for i in range(10):
        let img = Image(100, 100, ImageFormat.RGB, DataType.UINT8, DeviceType.CPU)
        images.append(img)
    
    print("✓ Created 10 images successfully")
    
    # Images will be automatically cleaned up when they go out of scope
    print("✓ Memory management test PASSED")

fn main():
    """Main test runner."""
    print_test_header()
    
    var total_suites = 0
    var passed_suites = 0
    
    # Run core tests
    total_suites += 1
    try:
        run_core_tests()
        passed_suites += 1
        print("✓ Core tests completed successfully\n")
    except:
        print("✗ Core tests failed\n")
    
    # Run operations tests
    total_suites += 1
    try:
        run_operations_tests()
        passed_suites += 1
        print("✓ Operations tests completed successfully\n")
    except:
        print("✗ Operations tests failed\n")
    
    # Run integration tests
    total_suites += 1
    try:
        if run_integration_tests():
            passed_suites += 1
            print("✓ Integration tests completed successfully\n")
        else:
            print("✗ Integration tests failed\n")
    except:
        print("✗ Integration tests failed with exception\n")
    
    # Run memory tests
    total_suites += 1
    try:
        run_memory_tests()
        passed_suites += 1
        print("✓ Memory tests completed successfully\n")
    except:
        print("✗ Memory tests failed\n")
    
    # Run placeholder tests (these don't affect pass/fail count)
    run_filter_tests()
    print("")
    
    run_gpu_tests()
    print("")
    
    run_io_tests()
    print("")
    
    run_performance_tests()
    print("")
    
    # Print final summary
    print_test_footer(total_suites, passed_suites)

if __name__ == "__main__":
    main()
