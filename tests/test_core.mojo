"""
Unit tests for core image functionality.

This module contains tests for the core Image struct, Pixel operations,
and basic image manipulation functions.
"""

from image_gpu.core.image import Image, Pixel
from image_gpu.core.types import ImageFormat, DataType, ColorSpace, DeviceType
from image_gpu.io.loader import create_test_image, create_solid_color_image

# Simple test framework
struct TestResult:
    """Structure to hold test results."""
    var test_name: String
    var passed: Bool
    var message: String
    
    fn __init__(inout self, name: String, passed: Bool, message: String = ""):
        self.test_name = name
        self.passed = passed
        self.message = message
    
    fn print_result(self):
        """Print test result."""
        let status = "PASS" if self.passed else "FAIL"
        print("[" + status + "] " + self.test_name)
        if not self.passed and self.message != "":
            print("  Error: " + self.message)

struct TestSuite:
    """Test suite runner."""
    var tests_run: Int
    var tests_passed: Int
    var results: List[TestResult]
    
    fn __init__(inout self):
        self.tests_run = 0
        self.tests_passed = 0
        self.results = List[TestResult]()
    
    fn run_test(inout self, test_name: String, test_func: fn() -> Bool) -> Bool:
        """Run a single test."""
        self.tests_run += 1
        let passed = test_func()
        if passed:
            self.tests_passed += 1
        
        let result = TestResult(test_name, passed)
        self.results.append(result)
        result.print_result()
        return passed
    
    fn print_summary(self):
        """Print test summary."""
        print("\n" + "="*50)
        print("TEST SUMMARY")
        print("="*50)
        print("Tests run: " + str(self.tests_run))
        print("Tests passed: " + str(self.tests_passed))
        print("Tests failed: " + str(self.tests_run - self.tests_passed))
        print("Success rate: " + str(Float64(self.tests_passed) / Float64(self.tests_run) * 100.0) + "%")

# Test functions
fn test_pixel_creation() -> Bool:
    """Test Pixel struct creation and basic operations."""
    # Test default pixel
    let default_pixel = Pixel()
    if default_pixel.r != 0.0 or default_pixel.g != 0.0 or default_pixel.b != 0.0 or default_pixel.a != 1.0:
        return False
    
    # Test colored pixel
    let red_pixel = Pixel(1.0, 0.0, 0.0, 1.0)
    if red_pixel.r != 1.0 or red_pixel.g != 0.0 or red_pixel.b != 0.0 or red_pixel.a != 1.0:
        return False
    
    # Test grayscale pixel
    let gray_pixel = Pixel(0.5)
    if gray_pixel.r != 0.5 or gray_pixel.g != 0.5 or gray_pixel.b != 0.5 or gray_pixel.a != 1.0:
        return False
    
    return True

fn test_pixel_luminance() -> Bool:
    """Test pixel luminance calculation."""
    let white_pixel = Pixel(1.0, 1.0, 1.0, 1.0)
    let white_luminance = white_pixel.luminance()
    if abs(white_luminance - 1.0) > 0.001:
        return False
    
    let black_pixel = Pixel(0.0, 0.0, 0.0, 1.0)
    let black_luminance = black_pixel.luminance()
    if abs(black_luminance - 0.0) > 0.001:
        return False
    
    # Test standard luminance weights (0.299*R + 0.587*G + 0.114*B)
    let test_pixel = Pixel(1.0, 0.0, 0.0, 1.0)  # Pure red
    let expected_luminance = 0.299
    if abs(test_pixel.luminance() - expected_luminance) > 0.001:
        return False
    
    return True

fn test_pixel_grayscale_conversion() -> Bool:
    """Test pixel to grayscale conversion."""
    let colored_pixel = Pixel(1.0, 0.5, 0.0, 1.0)
    let gray_pixel = colored_pixel.to_grayscale()
    
    let expected_gray = colored_pixel.luminance()
    if abs(gray_pixel.r - expected_gray) > 0.001:
        return False
    if abs(gray_pixel.g - expected_gray) > 0.001:
        return False
    if abs(gray_pixel.b - expected_gray) > 0.001:
        return False
    if gray_pixel.a != colored_pixel.a:
        return False
    
    return True

fn test_image_creation() -> Bool:
    """Test Image struct creation."""
    let width = 100
    let height = 50
    let image = Image(width, height, ImageFormat.RGB, DataType.UINT8)
    
    if image.width != width:
        return False
    if image.height != height:
        return False
    if image.format != ImageFormat.RGB:
        return False
    if image.channels != 3:
        return False
    if image.data_type != DataType.UINT8:
        return False
    
    return True

fn test_image_pixel_operations() -> Bool:
    """Test image pixel get/set operations."""
    var image = Image(10, 10, ImageFormat.RGB, DataType.UINT8)
    
    # Test setting and getting pixels
    let test_pixel = Pixel(0.5, 0.7, 0.3, 1.0)
    image.set_pixel_uint8(5, 5, test_pixel)
    let retrieved_pixel = image.get_pixel_uint8(5, 5)
    
    # Allow for small floating point errors due to uint8 conversion
    let tolerance = 1.0 / 255.0 + 0.001
    if abs(retrieved_pixel.r - test_pixel.r) > tolerance:
        return False
    if abs(retrieved_pixel.g - test_pixel.g) > tolerance:
        return False
    if abs(retrieved_pixel.b - test_pixel.b) > tolerance:
        return False
    
    return True

fn test_image_bounds_checking() -> Bool:
    """Test image bounds checking."""
    let image = Image(10, 10, ImageFormat.RGB, DataType.UINT8)
    
    # Test valid coordinates
    if not image.is_valid_coords(0, 0):
        return False
    if not image.is_valid_coords(9, 9):
        return False
    if not image.is_valid_coords(5, 5):
        return False
    
    # Test invalid coordinates
    if image.is_valid_coords(-1, 0):
        return False
    if image.is_valid_coords(0, -1):
        return False
    if image.is_valid_coords(10, 0):
        return False
    if image.is_valid_coords(0, 10):
        return False
    
    return True

fn test_image_fill() -> Bool:
    """Test image fill operation."""
    var image = Image(5, 5, ImageFormat.RGB, DataType.UINT8)
    let fill_pixel = Pixel(0.8, 0.2, 0.6, 1.0)
    
    image.fill(fill_pixel)
    
    # Check that all pixels are filled correctly
    for y in range(image.height):
        for x in range(image.width):
            let pixel = image.get_pixel_uint8(x, y)
            let tolerance = 1.0 / 255.0 + 0.001
            
            if abs(pixel.r - fill_pixel.r) > tolerance:
                return False
            if abs(pixel.g - fill_pixel.g) > tolerance:
                return False
            if abs(pixel.b - fill_pixel.b) > tolerance:
                return False
    
    return True

fn test_image_clone() -> Bool:
    """Test image cloning."""
    var original = Image(8, 8, ImageFormat.RGB, DataType.UINT8)
    let test_pixel = Pixel(0.4, 0.6, 0.8, 1.0)
    original.set_pixel_uint8(3, 3, test_pixel)
    
    let cloned = original.clone()
    
    # Check dimensions match
    if cloned.width != original.width or cloned.height != original.height:
        return False
    
    # Check format matches
    if cloned.format != original.format or cloned.data_type != original.data_type:
        return False
    
    # Check pixel data matches
    let original_pixel = original.get_pixel_uint8(3, 3)
    let cloned_pixel = cloned.get_pixel_uint8(3, 3)
    
    let tolerance = 1.0 / 255.0 + 0.001
    if abs(original_pixel.r - cloned_pixel.r) > tolerance:
        return False
    if abs(original_pixel.g - cloned_pixel.g) > tolerance:
        return False
    if abs(original_pixel.b - cloned_pixel.b) > tolerance:
        return False
    
    return True

fn test_image_size_calculation() -> Bool:
    """Test image size calculation."""
    let rgb_image = Image(100, 50, ImageFormat.RGB, DataType.UINT8)
    let expected_rgb_size = 100 * 50 * 3 * 1  # width * height * channels * bytes_per_channel
    if rgb_image.size_bytes() != expected_rgb_size:
        return False
    
    let rgba_image = Image(100, 50, ImageFormat.RGBA, DataType.UINT8)
    let expected_rgba_size = 100 * 50 * 4 * 1
    if rgba_image.size_bytes() != expected_rgba_size:
        return False
    
    return True

fn test_different_image_formats() -> Bool:
    """Test different image formats."""
    # Test RGB format
    let rgb_image = Image(10, 10, ImageFormat.RGB, DataType.UINT8)
    if rgb_image.channels != 3:
        return False
    
    # Test RGBA format
    let rgba_image = Image(10, 10, ImageFormat.RGBA, DataType.UINT8)
    if rgba_image.channels != 4:
        return False
    
    # Test grayscale format
    let gray_image = Image(10, 10, ImageFormat.GRAYSCALE, DataType.UINT8)
    if gray_image.channels != 1:
        return False
    
    return True

fn run_core_tests():
    """Run all core tests."""
    print("Running Core Image Tests")
    print("========================")
    
    var suite = TestSuite()
    
    # Run pixel tests
    suite.run_test("Pixel Creation", test_pixel_creation)
    suite.run_test("Pixel Luminance", test_pixel_luminance)
    suite.run_test("Pixel Grayscale Conversion", test_pixel_grayscale_conversion)
    
    # Run image tests
    suite.run_test("Image Creation", test_image_creation)
    suite.run_test("Image Pixel Operations", test_image_pixel_operations)
    suite.run_test("Image Bounds Checking", test_image_bounds_checking)
    suite.run_test("Image Fill", test_image_fill)
    suite.run_test("Image Clone", test_image_clone)
    suite.run_test("Image Size Calculation", test_image_size_calculation)
    suite.run_test("Different Image Formats", test_different_image_formats)
    
    suite.print_summary()

fn main():
    """Main test function."""
    run_core_tests()

# Helper function for floating point comparison
fn abs(value: Float32) -> Float32:
    return value if value >= 0.0 else -value

if __name__ == "__main__":
    main()
