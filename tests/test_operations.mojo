"""
Unit tests for image operations.

This module contains tests for image processing operations like
resize, crop, rotate, and color adjustments.
"""

from image_gpu.core.image import Image, Pixel
from image_gpu.core.operations import (
    resize_image, crop_image, rotate_image, flip_horizontal, flip_vertical,
    convert_to_grayscale, adjust_brightness, adjust_contrast
)
from image_gpu.core.types import ImageFormat, DataType, DeviceType
from image_gpu.io.loader import create_solid_color_image, create_test_image

# Import test framework from test_core
from test_core import TestResult, TestSuite

fn test_resize_image() -> Bool:
    """Test image resizing functionality."""
    let original = create_test_image(100, 100, ImageFormat.RGB, DeviceType.CPU)
    
    # Test resize to smaller dimensions
    let smaller = resize_image(original, 50, 50, "nearest")
    if smaller.width != 50 or smaller.height != 50:
        return False
    
    # Test resize to larger dimensions
    let larger = resize_image(original, 200, 150, "bilinear")
    if larger.width != 200 or larger.height != 150:
        return False
    
    # Test that format is preserved
    if smaller.format != original.format or larger.format != original.format:
        return False
    
    return True

fn test_crop_image() -> Bool:
    """Test image cropping functionality."""
    let original = create_solid_color_image(100, 100, 1.0, 0.0, 0.0)  # Red image
    
    # Test normal crop
    let cropped = crop_image(original, 25, 25, 50, 50)
    if cropped.width != 50 or cropped.height != 50:
        return False
    
    # Test that cropped image has same color
    let pixel = cropped.get_pixel_uint8(25, 25)
    if abs(pixel.r - 1.0) > 0.01 or abs(pixel.g - 0.0) > 0.01 or abs(pixel.b - 0.0) > 0.01:
        return False
    
    # Test crop at edge (should be clamped)
    let edge_crop = crop_image(original, 90, 90, 20, 20)
    if edge_crop.width != 10 or edge_crop.height != 10:  # Should be clamped to available area
        return False
    
    return True

fn test_flip_operations() -> Bool:
    """Test horizontal and vertical flip operations."""
    # Create a test image with distinct colors in corners
    var test_image = Image(4, 4, ImageFormat.RGB, DataType.UINT8)
    
    # Set corner pixels to different colors
    test_image.set_pixel_uint8(0, 0, Pixel(1.0, 0.0, 0.0))  # Top-left: Red
    test_image.set_pixel_uint8(3, 0, Pixel(0.0, 1.0, 0.0))  # Top-right: Green
    test_image.set_pixel_uint8(0, 3, Pixel(0.0, 0.0, 1.0))  # Bottom-left: Blue
    test_image.set_pixel_uint8(3, 3, Pixel(1.0, 1.0, 0.0))  # Bottom-right: Yellow
    
    # Test horizontal flip
    let h_flipped = flip_horizontal(test_image)
    let h_top_left = h_flipped.get_pixel_uint8(0, 0)
    let h_top_right = h_flipped.get_pixel_uint8(3, 0)
    
    # After horizontal flip, top-left should be green, top-right should be red
    if abs(h_top_left.g - 1.0) > 0.01 or abs(h_top_right.r - 1.0) > 0.01:
        return False
    
    # Test vertical flip
    let v_flipped = flip_vertical(test_image)
    let v_top_left = v_flipped.get_pixel_uint8(0, 0)
    let v_bottom_left = v_flipped.get_pixel_uint8(0, 3)
    
    # After vertical flip, top-left should be blue, bottom-left should be red
    if abs(v_top_left.b - 1.0) > 0.01 or abs(v_bottom_left.r - 1.0) > 0.01:
        return False
    
    return True

fn test_grayscale_conversion() -> Bool:
    """Test grayscale conversion."""
    let colored_image = create_solid_color_image(10, 10, 1.0, 0.5, 0.0)  # Orange
    let gray_image = convert_to_grayscale(colored_image)
    
    # Check that result is grayscale format
    if gray_image.format != ImageFormat.GRAYSCALE:
        return False
    
    # Check that dimensions are preserved
    if gray_image.width != colored_image.width or gray_image.height != colored_image.height:
        return False
    
    # Check that grayscale value is correct (using luminance formula)
    let gray_pixel = gray_image.get_pixel_uint8(5, 5)
    let expected_luminance = 0.299 * 1.0 + 0.587 * 0.5 + 0.114 * 0.0
    if abs(gray_pixel.r - expected_luminance) > 0.05:  # Allow some tolerance for uint8 conversion
        return False
    
    return True

fn test_brightness_adjustment() -> Bool:
    """Test brightness adjustment."""
    let original = create_solid_color_image(10, 10, 0.5, 0.5, 0.5)  # Gray
    
    # Test brightness increase
    let brighter = adjust_brightness(original, 1.5)
    let bright_pixel = brighter.get_pixel_uint8(5, 5)
    
    # Should be brighter but clamped to 1.0
    if abs(bright_pixel.r - 0.75) > 0.05:  # 0.5 * 1.5 = 0.75
        return False
    
    # Test brightness decrease
    let darker = adjust_brightness(original, 0.5)
    let dark_pixel = darker.get_pixel_uint8(5, 5)
    
    if abs(dark_pixel.r - 0.25) > 0.05:  # 0.5 * 0.5 = 0.25
        return False
    
    return True

fn test_contrast_adjustment() -> Bool:
    """Test contrast adjustment."""
    let original = create_solid_color_image(10, 10, 0.7, 0.7, 0.7)  # Light gray
    
    # Test contrast increase
    let high_contrast = adjust_contrast(original, 2.0)
    let contrast_pixel = high_contrast.get_pixel_uint8(5, 5)
    
    # Contrast formula: (pixel - 0.5) * factor + 0.5
    let expected = (0.7 - 0.5) * 2.0 + 0.5  # = 0.9
    if abs(contrast_pixel.r - expected) > 0.05:
        return False
    
    return True

fn test_rotate_image() -> Bool:
    """Test image rotation."""
    let original = create_test_image(20, 20, ImageFormat.RGB, DeviceType.CPU)
    
    # Test 90-degree rotation
    let rotated_90 = rotate_image(original, 90.0)
    
    # Dimensions should be swapped for 90-degree rotation (approximately)
    # Note: Actual implementation may vary due to bounding box calculation
    if rotated_90.width == 0 or rotated_90.height == 0:
        return False
    
    # Test 180-degree rotation
    let rotated_180 = rotate_image(original, 180.0)
    
    # Dimensions should be similar for 180-degree rotation
    if rotated_180.width == 0 or rotated_180.height == 0:
        return False
    
    return True

fn test_operation_chain() -> Bool:
    """Test chaining multiple operations."""
    let original = create_test_image(50, 50, ImageFormat.RGB, DeviceType.CPU)
    
    # Chain multiple operations
    var processed = original
    processed = resize_image(processed, 40, 40, "bilinear")
    processed = adjust_brightness(processed, 1.2)
    processed = flip_horizontal(processed)
    processed = convert_to_grayscale(processed)
    
    # Check final result
    if processed.width != 40 or processed.height != 40:
        return False
    
    if processed.format != ImageFormat.GRAYSCALE:
        return False
    
    return True

fn test_edge_cases() -> Bool:
    """Test edge cases and error conditions."""
    let original = create_test_image(10, 10, ImageFormat.RGB, DeviceType.CPU)
    
    # Test resize to zero dimensions (should handle gracefully)
    # Note: Implementation should handle this case appropriately
    
    # Test crop with invalid coordinates
    let invalid_crop = crop_image(original, -5, -5, 5, 5)
    if invalid_crop.width != 5 or invalid_crop.height != 5:
        return False
    
    # Test extreme brightness values
    let extreme_bright = adjust_brightness(original, 10.0)
    let bright_pixel = extreme_bright.get_pixel_uint8(5, 5)
    
    # Should be clamped to 1.0
    if bright_pixel.r > 1.01 or bright_pixel.g > 1.01 or bright_pixel.b > 1.01:
        return False
    
    return True

fn run_operations_tests():
    """Run all operations tests."""
    print("Running Image Operations Tests")
    print("==============================")
    
    var suite = TestSuite()
    
    # Run operation tests
    suite.run_test("Resize Image", test_resize_image)
    suite.run_test("Crop Image", test_crop_image)
    suite.run_test("Flip Operations", test_flip_operations)
    suite.run_test("Grayscale Conversion", test_grayscale_conversion)
    suite.run_test("Brightness Adjustment", test_brightness_adjustment)
    suite.run_test("Contrast Adjustment", test_contrast_adjustment)
    suite.run_test("Rotate Image", test_rotate_image)
    suite.run_test("Operation Chain", test_operation_chain)
    suite.run_test("Edge Cases", test_edge_cases)
    
    suite.print_summary()

fn main():
    """Main test function."""
    run_operations_tests()

# Helper function for floating point comparison
fn abs(value: Float32) -> Float32:
    return value if value >= 0.0 else -value

if __name__ == "__main__":
    main()
